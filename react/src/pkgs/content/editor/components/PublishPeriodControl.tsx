import { DateTimeNullable } from '@/common/components/dates/DateTimeNullable'
import { addDays } from '@/common/components/dates/helpers'
import { PublishPeriod } from '@/pkgs/content/types'

type PublishPeriodDatesProps<T extends PublishPeriod = PublishPeriod> = {
    value: T
    onChange: (v: T) => void
    errors?: Record<string, string>
    disabled?: boolean
}

export const PublishPeriodControl = ({ value, onChange, errors, disabled }: PublishPeriodDatesProps) => {
    return (
        <div>
            <DateTimeNullable
                value={value.PublishAt}
                onChange={(date) => onChange({ ...value, PublishAt: date })}
                label={'Publish date'}
                max={value.ExpireAt}
                defaultValue={addDays(value.ExpireAt, -1)}
                disabled={disabled}
            />
            <DateTimeNullable
                value={value.ExpireAt}
                onChange={(date) => onChange({ ...value, ExpireAt: date })}
                label={'Expiry date'}
                min={value.PublishAt}
                defaultValue={addDays(value.PublishAt, 90)}
                disabled={disabled}
            />
        </div>
    )
}
